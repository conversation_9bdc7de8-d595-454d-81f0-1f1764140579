{"total_tasks": 162, "successful_downloads": 0, "failed_downloads": 162, "tasks_by_category": {"issue_tasks": 17, "rule_tasks": 5, "conclusion_tasks": 12, "other_tasks": 128}, "failed_tasks": [{"task": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "canada_tax_court_outcomes", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "citation_prediction_classification", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "citation_prediction_open", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "consumer_contracts_qa", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "contract_nli_confidentiality_of_agreement", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "contract_nli_explicit_identification", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "contract_nli_inclusion_of_verbally_conveyed_information", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "contract_nli_limited_use", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "contract_nli_no_licensing", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "contract_nli_notice_on_compelled_disclosure", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "contract_nli_permissible_acquirement_of_similar_information", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "contract_nli_permissible_copy", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "contract_nli_permissible_development_of_similar_information", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "contract_nli_permissible_post-agreement_possession", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "contract_nli_return_of_confidential_information", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "contract_nli_sharing_with_employees", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "contract_nli_sharing_with_third-parties", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "contract_nli_survival_of_obligations", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "contract_qa", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "corporate_lobbying", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "cuad_affiliate_license-licensee", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "cuad_affiliate_license-licensor", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "cuad_anti-assignment", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "cuad_audit_rights", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "cuad_cap_on_liability", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "cuad_change_of_control", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "cuad_competitive_restriction_exception", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "cuad_covenant_not_to_sue", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "cuad_effective_date", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "cuad_exclusivity", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "cuad_expiration_date", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "cuad_governing_law", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "cuad_insurance", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "cuad_ip_ownership_assignment", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "cuad_irrevocable_or_perpetual_license", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "cuad_joint_ip_ownership", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "cuad_license_grant", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "cuad_liquidated_damages", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "cuad_minimum_commitment", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "cuad_most_favored_nation", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "cuad_no-solicit_of_customers", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "cuad_no-solicit_of_employees", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "cuad_non-compete", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "cuad_non-disparagement", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "cuad_non-transferable_license", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "cuad_notice_period_to_terminate_renewal", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "cuad_post-termination_services", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "cuad_price_restrictions", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "cuad_renewal_term", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "cuad_revenue-profit_sharing", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "cuad_rofr-rofo-rofn", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "cuad_source_code_escrow", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "cuad_termination_for_convenience", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "cuad_third_party_beneficiary", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "cuad_uncapped_liability", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "cuad_unlimited-all-you-can-eat-license", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "cuad_volume_restriction", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "cuad_warranty_duration", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "definition_classification", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "definition_extraction", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "diversity_1", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "diversity_2", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "diversity_3", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "diversity_4", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "diversity_5", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "diversity_6", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "function_of_decision_section", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "hearsay", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "insurance_policy_interpretation", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "jcrew_blocker", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "international_citizenship_questions", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "nys_judicial_ethics", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "learned_hands_benefits", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "learned_hands_business", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "learned_hands_consumer", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "learned_hands_courts", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "learned_hands_crime", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "learned_hands_divorce", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "learned_hands_domestic_violence", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "learned_hands_education", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "learned_hands_employment", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "learned_hands_estates", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "learned_hands_family", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "learned_hands_health", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "learned_hands_housing", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "learned_hands_immigration", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "learned_hands_torts", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "learned_hands_traffic", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "legal_reasoning_causality", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "maud_ability_to_consummate_concept_is_subject_to_mae_carveouts", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "maud_financial_point_of_view_is_the_sole_consideration", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "maud_accuracy_of_fundamental_target_rws_bringdown_standard", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "maud_accuracy_of_target_general_rw_bringdown_timing_answer", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "maud_accuracy_of_target_capitalization_rw_(outstanding_shares)_bringdown_standard_answer", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "maud_additional_matching_rights_period_for_modifications_(cor)", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "maud_application_of_buyer_consent_requirement_(negative_interim_covenant)", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "maud_buyer_consent_requirement_(ordinary_course)", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "maud_change_in_law__subject_to_disproportionate_impact_modifier", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "maud_changes_in_gaap_or_other_accounting_principles__subject_to_disproportionate_impact_modifier", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "maud_cor_permitted_in_response_to_intervening_event", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "maud_cor_permitted_with_board_fiduciary_determination_only", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "maud_cor_standard_(intervening_event)", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "maud_cor_standard_(superior_offer)", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "maud_definition_contains_knowledge_requirement_-_answer", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "maud_definition_includes_asset_deals", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "maud_definition_includes_stock_deals", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "maud_fiduciary_exception__board_determination_standard", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "maud_fiduciary_exception_board_determination_trigger_(no_shop)", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "maud_fls_(mae)_standard", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "maud_general_economic_and_financial_conditions_subject_to_disproportionate_impact_modifier", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "maud_includes_consistent_with_past_practice", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "maud_initial_matching_rights_period_(cor)", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "maud_initial_matching_rights_period_(ftr)", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "maud_intervening_event_-_required_to_occur_after_signing_-_answer", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "maud_knowledge_definition", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "maud_liability_standard_for_no-shop_breach_by_target_non-do_representatives", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "maud_ordinary_course_efforts_standard", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "maud_pandemic_or_other_public_health_event__subject_to_disproportionate_impact_modifier", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "maud_pandemic_or_other_public_health_event_specific_reference_to_pandemic-related_governmental_responses_or_measures", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "maud_relational_language_(mae)_applies_to", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "maud_specific_performance", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "maud_tail_period_length", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "maud_type_of_consideration", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "opp115_data_retention", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "opp115_data_security", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "opp115_do_not_track", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "opp115_first_party_collection_use", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "opp115_international_and_specific_audiences", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "opp115_policy_change", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "opp115_third_party_sharing_collection", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "opp115_user_access,_edit_and_deletion", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "opp115_user_choice_control", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "oral_argument_question_purpose", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "overruling", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "personal_jurisdiction", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "privacy_policy_entailment", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "privacy_policy_qa", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "proa", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "rule_qa", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "scalr", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "ssla_company_defendants", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "ssla_individual_defendants", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "ssla_plaintiff", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "sara_entailment", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "sara_numeric", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "successor_liability", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "supply_chain_disclosure_best_practice_accountability", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "supply_chain_disclosure_best_practice_audits", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "supply_chain_disclosure_best_practice_certification", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "supply_chain_disclosure_best_practice_training", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "supply_chain_disclosure_best_practice_verification", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "supply_chain_disclosure_disclosed_accountability", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "supply_chain_disclosure_disclosed_audits", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "supply_chain_disclosure_disclosed_certification", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "supply_chain_disclosure_disclosed_training", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "supply_chain_disclosure_disclosed_verification", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "telemarketing_sales_rule", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "textualism_tool_dictionaries", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "textualism_tool_plain", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "ucc_v_common_law", "error": "Dataset scripts are no longer supported, but found legalbench.py"}, {"task": "unfair_tos", "error": "Dataset scripts are no longer supported, but found legalbench.py"}]}