#!/usr/bin/env python3
"""
Updated LegalBench Dataset Downloader for RAG Pipeline Evaluation
Works with the current HuggingFace datasets API (no dataset scripts)
"""

import os
import sys
import json
import pandas as pd
from pathlib import Path
from typing import Dict, List, Any, Optional
from collections import Counter
import time

def install_requirements():
    """Install required packages if not available."""
    required_packages = ["datasets>=2.0.0", "pandas", "tqdm", "huggingface-hub"]
    missing_packages = []

    for package in required_packages:
        try:
            if "datasets" in package:
                import datasets
            elif "pandas" in package:
                import pandas
            elif "tqdm" in package:
                import tqdm
            elif "huggingface-hub" in package:
                import huggingface_hub
        except ImportError:
            missing_packages.append(package.split(">=")[0])

    if missing_packages:
        print(f"Missing required packages: {missing_packages}")
        print("Installing required packages...")
        import subprocess
        for package in missing_packages:
            subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print("Packages installed successfully!")

# Global imports after installation
try:
    from datasets import load_dataset
    from tqdm import tqdm
    import datasets
except ImportError:
    # These will be imported after install_requirements() is called
    load_dataset = None
    tqdm = None
    datasets = None

def get_task_lists():
    """Get task lists from the existing legalbench directory."""
    sys.path.append(str(Path(__file__).parent / "legalbench"))
    
    try:
        from tasks import TASKS, ISSUE_TASKS, RULE_TASKS, CONCLUSION_TASKS
        print(f"✅ Loaded {len(TASKS)} tasks from legalbench/tasks.py")
        return TASKS, ISSUE_TASKS, RULE_TASKS, CONCLUSION_TASKS
    except ImportError as e:
        print(f"⚠️ Could not import from legalbench/tasks.py: {e}")
        print("📝 Using a subset of high-priority tasks for testing...")
        
        # High-priority tasks for RAG evaluation based on previous analysis
        TASKS = [
            # High-priority interpretation tasks
            "jcrew_blocker", "contract_qa", "consumer_contracts_qa", 
            "cuad_governing_law", "cuad_ip_ownership_assignment",
            
            # Legal reasoning tasks
            "hearsay", "abercrombie", "personal_jurisdiction",
            
            # Document analysis tasks
            "privacy_policy_qa", "definition_extraction",
            
            # Issue spotting tasks
            "learned_hands_housing", "learned_hands_employment",
            
            # Rule-based tasks
            "rule_qa", "international_citizenship_questions"
        ]
        
        ISSUE_TASKS = ["learned_hands_housing", "learned_hands_employment"]
        RULE_TASKS = ["rule_qa", "international_citizenship_questions"]
        CONCLUSION_TASKS = ["hearsay", "abercrombie", "personal_jurisdiction"]
        
        return TASKS, ISSUE_TASKS, RULE_TASKS, CONCLUSION_TASKS

def categorize_task(task_name: str, issue_tasks: List[str], rule_tasks: List[str], conclusion_tasks: List[str]) -> str:
    """Categorize a task based on predefined lists."""
    if task_name in issue_tasks:
        return "issue_spotting"
    elif task_name in rule_tasks:
        return "rule_based"
    elif task_name in conclusion_tasks:
        return "conclusion"
    elif any(keyword in task_name.lower() for keyword in ["contract", "cuad", "privacy", "policy"]):
        return "interpretation"
    elif any(keyword in task_name.lower() for keyword in ["definition", "extract"]):
        return "rhetorical_analysis"
    else:
        return "other"

def assess_rag_suitability(task_data: pd.DataFrame, task_name: str) -> Dict[str, Any]:
    """Assess how suitable a task is for RAG evaluation."""
    suitability = {
        "suitable": False,
        "complexity": "low",
        "context_dependency": "low",
        "avg_text_length": 0,
        "reasons": [],
        "rag_score": 0
    }
    
    if "text" not in task_data.columns:
        suitability["reasons"].append("No 'text' column for retrieval")
        return suitability
    
    # Calculate average text length
    text_lengths = [len(str(text)) for text in task_data["text"] if pd.notna(text)]
    if not text_lengths:
        suitability["reasons"].append("No valid text data")
        return suitability
        
    avg_length = sum(text_lengths) / len(text_lengths)
    max_length = max(text_lengths)
    suitability["avg_text_length"] = int(avg_length)
    
    score = 0
    
    # Score based on text length (longer texts = better for RAG)
    if avg_length > 1000:
        suitability["context_dependency"] = "high"
        suitability["complexity"] = "high"
        score += 3
        suitability["reasons"].append(f"Long context texts (avg: {int(avg_length)} chars)")
    elif avg_length > 500:
        suitability["context_dependency"] = "medium"
        suitability["complexity"] = "medium"
        score += 2
        suitability["reasons"].append(f"Medium-length texts (avg: {int(avg_length)} chars)")
    elif avg_length > 200:
        suitability["context_dependency"] = "medium"
        score += 1
        suitability["reasons"].append(f"Moderate text length (avg: {int(avg_length)} chars)")
    
    # Score based on task type (document analysis tasks are better for RAG)
    if any(keyword in task_name.lower() for keyword in ["contract", "cuad", "policy", "agreement"]):
        score += 2
        suitability["reasons"].append("Legal document analysis task")
    elif any(keyword in task_name.lower() for keyword in ["qa", "question"]):
        score += 1
        suitability["reasons"].append("Question-answering format")
    
    # Check content complexity
    text_content = " ".join(str(text).lower() for text in task_data["text"][:5])  # Sample first 5
    if any(keyword in text_content for keyword in ["shall", "whereas", "pursuant", "heretofore"]):
        score += 1
        suitability["reasons"].append("Complex legal language")
    
    # Dataset size consideration
    if len(task_data) > 100:
        score += 1
        suitability["reasons"].append(f"Large dataset ({len(task_data)} samples)")
    elif len(task_data) > 50:
        suitability["reasons"].append(f"Medium dataset ({len(task_data)} samples)")
    
    suitability["rag_score"] = score
    suitability["suitable"] = score >= 2
    
    if score >= 4:
        suitability["complexity"] = "high"
    elif score >= 2:
        suitability["complexity"] = "medium"
    
    return suitability

def download_single_task(task_name: str, base_dir: Path) -> Optional[Dict[str, Any]]:
    """Download a single task and return its info."""
    try:
        print(f"📥 Downloading {task_name}...")
        
        # Try different approaches to load the dataset
        dataset = None
        for attempt in range(3):
            try:
                dataset = load_dataset("nguha/legalbench", task_name, trust_remote_code=True)
                break
            except Exception as e:
                if attempt < 2:
                    print(f"   ⚠️ Attempt {attempt + 1} failed: {str(e)[:100]}... Retrying...")
                    time.sleep(1)
                else:
                    raise e
        
        if dataset is None:
            return None
        
        # Create task directory
        task_dir = base_dir / "tasks" / task_name
        task_dir.mkdir(exist_ok=True, parents=True)
        
        task_info = {
            "task_name": task_name,
            "train_size": 0,
            "test_size": 0,
            "features": [],
            "download_success": True
        }
        
        # Process train split
        if "train" in dataset:
            train_df = dataset["train"].to_pandas()
            train_df.to_csv(task_dir / "train.tsv", sep="\t", index=False)
            task_info["train_size"] = len(train_df)
            task_info["features"] = list(train_df.columns)
            print(f"   ✅ Train: {len(train_df)} samples")
        
        # Process test split
        test_df = None
        if "test" in dataset:
            test_df = dataset["test"].to_pandas()
            test_df.to_csv(task_dir / "test.tsv", sep="\t", index=False)
            task_info["test_size"] = len(test_df)
            task_info["features"] = list(test_df.columns)
            print(f"   ✅ Test: {len(test_df)} samples")
        
        # Save raw dataset info
        dataset_info = {
            "description": dataset.get("train", dataset.get("test", {})).info.description if hasattr(dataset.get("train", dataset.get("test", {})), "info") else "",
            "features": {k: str(v) for k, v in dataset.get("train", dataset.get("test", {})).features.items()},
            "splits": list(dataset.keys())
        }
        
        with open(task_dir / "dataset_info.json", "w") as f:
            json.dump(dataset_info, f, indent=2)
        
        return task_info, test_df
        
    except Exception as e:
        print(f"   ❌ Failed: {str(e)}")
        return None

def main():
    """Main function to download and organize LegalBench dataset."""
    global load_dataset, tqdm, datasets

    print("🚀 LegalBench Dataset Downloader for RAG Evaluation")
    print("=" * 60)

    # Install requirements first
    install_requirements()

    # Now import the modules
    from datasets import load_dataset
    from tqdm import tqdm
    import datasets

    # Get task lists
    TASKS, ISSUE_TASKS, RULE_TASKS, CONCLUSION_TASKS = get_task_lists()

    # Suppress HuggingFace progress bars
    datasets.utils.logging.set_verbosity_error()
    
    # Create directories
    base_dir = Path("legalbench_dataset")
    datasets_dir = base_dir / "consolidated"
    
    for dir_path in [base_dir, base_dir / "tasks", datasets_dir]:
        dir_path.mkdir(exist_ok=True, parents=True)
    
    print(f"📁 Base directory: {base_dir.absolute()}")
    print(f"📊 Tasks to download: {len(TASKS)}")
    print()
    
    # Track statistics
    stats = {
        "total_tasks": len(TASKS),
        "successful_downloads": 0,
        "failed_downloads": 0,
        "rag_suitable_tasks": 0,
        "failed_tasks": [],
        "task_categories": Counter()
    }
    
    all_task_data = []
    task_infos = []
    rag_suitable_tasks = []
    
    # Download tasks
    for task_name in tqdm(TASKS, desc="Downloading tasks"):
        result = download_single_task(task_name, load_dataset, base_dir)
        
        if result is None:
            stats["failed_downloads"] += 1
            stats["failed_tasks"].append(task_name)
            continue
        
        task_info, test_df = result
        
        # Categorize task
        category = categorize_task(task_name, ISSUE_TASKS, RULE_TASKS, CONCLUSION_TASKS)
        task_info["category"] = category
        stats["task_categories"][category] += 1
        
        # Assess RAG suitability
        if test_df is not None and len(test_df) > 0:
            rag_assessment = assess_rag_suitability(test_df, task_name)
            task_info["rag_suitability"] = rag_assessment
            
            if rag_assessment["suitable"]:
                stats["rag_suitable_tasks"] += 1
                rag_suitable_tasks.append(task_info)
                
                # Add to consolidated data
                test_df["task_name"] = task_name
                test_df["task_category"] = category
                test_df["rag_score"] = rag_assessment["rag_score"]
                all_task_data.append(test_df)
        
        # Save task info
        task_dir = base_dir / "tasks" / task_name
        with open(task_dir / "task_info.json", "w") as f:
            json.dump(task_info, f, indent=2)
        
        task_infos.append(task_info)
        stats["successful_downloads"] += 1
    
    # Create consolidated datasets
    print(f"\n📊 Creating consolidated datasets...")
    
    if all_task_data:
        # Combine all RAG-suitable data
        combined_df = pd.concat(all_task_data, ignore_index=True)
        combined_df.to_csv(datasets_dir / "rag_evaluation_dataset.tsv", sep="\t", index=False)
        combined_df.to_json(datasets_dir / "rag_evaluation_dataset.jsonl", orient="records", lines=True)
        
        # Create category-specific datasets
        for category in combined_df["task_category"].unique():
            if pd.notna(category):
                category_df = combined_df[combined_df["task_category"] == category]
                category_df.to_csv(datasets_dir / f"rag_{category}.tsv", sep="\t", index=False)
        
        # Create high-quality RAG dataset (score >= 4)
        high_quality_df = combined_df[combined_df["rag_score"] >= 4]
        if len(high_quality_df) > 0:
            high_quality_df.to_csv(datasets_dir / "rag_high_quality.tsv", sep="\t", index=False)
    
    # Save comprehensive statistics
    stats["task_infos"] = task_infos
    with open(base_dir / "download_stats.json", "w") as f:
        json.dump(stats, f, indent=2, default=str)
    
    # Generate RAG-specific recommendations
    rag_recommendations = {
        "high_priority_tasks": [
            task for task in rag_suitable_tasks 
            if task["rag_suitability"]["rag_score"] >= 4
        ],
        "medium_priority_tasks": [
            task for task in rag_suitable_tasks 
            if task["rag_suitability"]["rag_score"] in [2, 3]
        ],
        "category_distribution": dict(stats["task_categories"]),
        "total_rag_samples": len(combined_df) if all_task_data else 0
    }
    
    with open(datasets_dir / "rag_recommendations.json", "w") as f:
        json.dump(rag_recommendations, f, indent=2, default=str)
    
    # Print summary
    print_summary(stats, base_dir, rag_recommendations)
    
    return base_dir, stats, rag_recommendations

def print_summary(stats: Dict, base_dir: Path, rag_recs: Dict):
    """Print a comprehensive summary of the download and analysis."""
    print("\n" + "="*60)
    print("📊 LEGALBENCH DATASET DOWNLOAD SUMMARY")
    print("="*60)
    
    print(f"✅ Successfully downloaded: {stats['successful_downloads']}/{stats['total_tasks']} tasks")
    print(f"❌ Failed downloads: {stats['failed_downloads']}")
    
    if stats['failed_downloads'] > 0:
        print(f"\n❌ Failed tasks: {', '.join(stats['failed_tasks'][:5])}{'...' if len(stats['failed_tasks']) > 5 else ''}")
    
    print(f"\n🎯 RAG Evaluation Suitability:")
    print(f"   📊 RAG-suitable tasks: {stats['rag_suitable_tasks']}")
    print(f"   🏆 High-priority tasks: {len(rag_recs.get('high_priority_tasks', []))}")
    print(f"   ⭐ Medium-priority tasks: {len(rag_recs.get('medium_priority_tasks', []))}")
    print(f"   📝 Total RAG samples: {rag_recs.get('total_rag_samples', 0)}")
    
    print(f"\n🏷️ Task Category Distribution:")
    for category, count in stats['task_categories'].items():
        print(f"   - {category}: {count} tasks")
    
    print(f"\n📁 Files Created:")
    print(f"   📂 Individual tasks: {base_dir}/tasks/[task_name]/")
    print(f"   📊 Consolidated dataset: {base_dir}/consolidated/rag_evaluation_dataset.tsv")
    print(f"   🎯 RAG recommendations: {base_dir}/consolidated/rag_recommendations.json")
    print(f"   📈 Download stats: {base_dir}/download_stats.json")
    
    print(f"\n💡 Next Steps for RAG Pipeline:")
    print(f"   1. Use consolidated dataset for RAG evaluation")
    print(f"   2. Start with high-priority tasks for initial testing")
    print(f"   3. Use category-specific datasets for specialized evaluation")
    print(f"   4. Check task_info.json files for detailed task descriptions")
    
    print("="*60)

if __name__ == "__main__":
    try:
        base_dir, stats, recommendations = main()
        
        # Show some example high-priority tasks
        if recommendations.get("high_priority_tasks"):
            print(f"\n🌟 Top High-Priority RAG Tasks:")
            for i, task in enumerate(recommendations["high_priority_tasks"][:5]):
                score = task["rag_suitability"]["rag_score"]
                avg_len = task["rag_suitability"]["avg_text_length"]
                print(f"   {i+1}. {task['task_name']} (Score: {score}, Avg Length: {avg_len})")
        
        print(f"\n🎉 Dataset download complete!")
        print(f"📁 Dataset location: {base_dir.absolute()}")
        
    except KeyboardInterrupt:
        print(f"\n⚠️ Download interrupted by user")
    except Exception as e:
        print(f"\n❌ Download failed with error: {e}")
        raise